# Vehicle Management System - RESTful API

A comprehensive RESTful API for managing vehicle inventory with full CRUD operations, built with Express.js and EJS templating.

## Features

- **Complete CRUD Operations**: Create, Read, Update, Delete vehicles
- **RESTful API Endpoints**: JSON API for programmatic access
- **Web Interface**: User-friendly web interface with Bootstrap styling
- **Data Validation**: Server-side validation for all vehicle data
- **Responsive Design**: Mobile-friendly interface
- **In-Memory Storage**: Simple data persistence (easily replaceable with database)

## Vehicle Model

Each vehicle has the following properties:

```javascript
{
  id: Number,           // Auto-generated unique identifier
  vehicleName: String,  // Name of the vehicle (required)
  model: String,        // Vehicle model (required)
  price: Number,        // Price in USD (required)
  image: String,        // Image URL (optional, defaults to placeholder)
  desc: String,         // Description (optional)
  brand: String         // Vehicle brand (required)
}
```

## Installation & Setup

1. **Clone or download the project**
2. **Install dependencies:**
   ```bash
   npm install
   ```
3. **Start the server:**
   ```bash
   npm start
   ```
4. **Access the application:**
   - Web Interface: http://localhost:3000
   - API Base URL: http://localhost:3000/api/vehicles

## API Endpoints

### GET /api/vehicles
Get all vehicles

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "vehicleName": "Toyota Camry",
      "model": "Camry 2024",
      "price": 25000,
      "image": "https://via.placeholder.com/300x200?text=Toyota+Camry",
      "desc": "A reliable and fuel-efficient sedan perfect for daily commuting.",
      "brand": "Toyota"
    }
  ],
  "count": 1
}
```

### GET /api/vehicles/:id
Get a single vehicle by ID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "vehicleName": "Toyota Camry",
    "model": "Camry 2024",
    "price": 25000,
    "image": "https://via.placeholder.com/300x200?text=Toyota+Camry",
    "desc": "A reliable and fuel-efficient sedan perfect for daily commuting.",
    "brand": "Toyota"
  }
}
```

### POST /api/vehicles
Create a new vehicle

**Request Body:**
```json
{
  "vehicleName": "Honda Civic",
  "model": "Civic 2024",
  "price": 23000,
  "image": "https://example.com/civic.jpg",
  "desc": "Compact car with excellent safety ratings",
  "brand": "Honda"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Vehicle created successfully",
  "data": {
    "id": 3,
    "vehicleName": "Honda Civic",
    "model": "Civic 2024",
    "price": 23000,
    "image": "https://example.com/civic.jpg",
    "desc": "Compact car with excellent safety ratings",
    "brand": "Honda"
  }
}
```

### PUT /api/vehicles/:id
Update an existing vehicle

**Request Body:**
```json
{
  "vehicleName": "Honda Civic Updated",
  "model": "Civic 2024 Sport",
  "price": 24000,
  "image": "https://example.com/civic-updated.jpg",
  "desc": "Updated compact car with sport features",
  "brand": "Honda"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Vehicle updated successfully",
  "data": {
    "id": 1,
    "vehicleName": "Honda Civic Updated",
    "model": "Civic 2024 Sport",
    "price": 24000,
    "image": "https://example.com/civic-updated.jpg",
    "desc": "Updated compact car with sport features",
    "brand": "Honda"
  }
}
```

### DELETE /api/vehicles/:id
Delete a vehicle

**Response:**
```json
{
  "success": true,
  "message": "Vehicle deleted successfully",
  "data": {
    "id": 1,
    "vehicleName": "Honda Civic",
    "model": "Civic 2024",
    "price": 23000,
    "image": "https://example.com/civic.jpg",
    "desc": "Compact car with excellent safety ratings",
    "brand": "Honda"
  }
}
```

## Web Interface Routes

- `GET /` - Home page with vehicle list
- `GET /vehicles/new` - Form to add new vehicle
- `GET /vehicles/:id` - Vehicle details page
- `GET /vehicles/:id/edit` - Form to edit vehicle
- `POST /vehicles` - Create vehicle via form
- `POST /vehicles/:id/edit` - Update vehicle via form
- `POST /vehicles/:id/delete` - Delete vehicle via form

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Specific validation errors"]
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `404` - Not Found
- `500` - Internal Server Error

## Data Validation

Required fields:
- `vehicleName` - Must be non-empty string
- `model` - Must be non-empty string
- `price` - Must be positive number
- `brand` - Must be non-empty string

Optional fields:
- `image` - Must be valid URL if provided
- `desc` - Can be empty string

## Testing the API

### Using curl:

**Get all vehicles:**
```bash
curl http://localhost:3000/api/vehicles
```

**Get single vehicle:**
```bash
curl http://localhost:3000/api/vehicles/1
```

**Create new vehicle:**
```bash
curl -X POST http://localhost:3000/api/vehicles \
  -H "Content-Type: application/json" \
  -d '{
    "vehicleName": "BMW X5",
    "model": "X5 2024",
    "price": 65000,
    "brand": "BMW",
    "desc": "Luxury SUV with advanced features"
  }'
```

**Update vehicle:**
```bash
curl -X PUT http://localhost:3000/api/vehicles/1 \
  -H "Content-Type: application/json" \
  -d '{
    "vehicleName": "BMW X5 Updated",
    "model": "X5 2024 M Sport",
    "price": 70000,
    "brand": "BMW",
    "desc": "Updated luxury SUV with M Sport package"
  }'
```

**Delete vehicle:**
```bash
curl -X DELETE http://localhost:3000/api/vehicles/1
```

## Technologies Used

- **Backend**: Node.js, Express.js
- **Templating**: EJS
- **Frontend**: Bootstrap 5, Font Awesome
- **Data Storage**: In-memory (easily replaceable with database)

## Future Enhancements

- Database integration (MongoDB, PostgreSQL, etc.)
- User authentication and authorization
- Image upload functionality
- Search and filtering capabilities
- Pagination for large datasets
- API rate limiting
- Unit and integration tests

## License

This project is open source and available under the MIT License.
